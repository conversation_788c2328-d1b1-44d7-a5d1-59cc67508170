using GCP.Common;

namespace GCP.FunctionPool.Flow.Models
{
    public enum ControlOperationType
    {
        None,
        Break,
        Continue,
    }

    public class DataForEach
    {
        public string Name { get; set; }
        public string Description { get; set; }
        /// <summary>
        /// 来源数据
        /// </summary>
        public DataValue DataSource { get; set; }
    }

    public class DataDelay
    {
        public string Name { get; set; }
        public string Description { get; set; }
        /// <summary>
        /// 延迟时间，单位：毫秒
        /// </summary>
        public DataValue MillisecondsDelay { get; set; }
    }

    public class DataBranch
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string Type { get; set; } = "conditions";
        public ConditionInfo Conditions { get; set; }
        public string Script { get; set; }
    }

    public class DataScript
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string Script { get; set; }
    }

    public class DataThrowError
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public DataValue ErrorMessage { get; set; }
    }
}
