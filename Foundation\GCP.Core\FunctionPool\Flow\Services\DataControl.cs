using GCP.Common;
using GCP.FunctionPool.Flow.Models;
using GCP.Functions.Common;
using Jint;
using System.Collections;
using System.Text;

namespace GCP.FunctionPool.Flow.Services
{
    class DataControl : DataBaseService
    {

        [Function("dataForEach", "迭代字典/数组")]
        public object ForEachData(DataForEach data)
        {
            var listObj = GetDataValue(data.DataSource);
            var sourceList = new ArrayList();

            if (listObj is not ICollection list)
            {
                sourceList.Add(listObj);
            }
            else
            {
                sourceList = new ArrayList(list);
            }

            return sourceList;
        }

        [Function("controlLoop", "循环")]
        public void ControlLoop()
        {
            
        }

        [Function("dataBranch", "如果")]
        public bool BranchData(DataBranch data)
        {
            var engine = this.GetEngine();
            var result = false;

            // 记录可视化文本
            if (this.Context.Persistence)
            {
                this.Context.Current.ArgsBuilder = new StringBuilder();
                this.Context.Current.ArgsBuilder.AppendLine($"判断类型: {data.Type}");
            }

            if(data.Type == "script")
            {
                var jsValue = engine.Evaluate(data.Script);
                result = jsValue.AsBoolean();

                if (this.Context.Persistence)
                {
                    this.Context.Current.ArgsBuilder.AppendLine($"脚本内容: {data.Script}");
                }
            }
            else
            {
                result = FlowUtils.GetConditionValue(data.Conditions, engine: engine, context: this.Context);

                if (this.Context.Persistence)
                {
                    RecordConditionDetails(data.Conditions, engine);
                }
            }

            if (this.Context.Persistence)
            {
                this.Context.Current.ArgsBuilder.AppendLine($"判断结果: {result}");
            }

            return result;
        }

        /// <summary>
        /// 记录条件判断的详细信息
        /// </summary>
        private void RecordConditionDetails(ConditionInfo condition, Engine engine, int level = 0)
        {
            if (condition == null) return;

            var indent = new string(' ', level * 2);

            if (condition.Type == ConditionInfoType.Node)
            {
                this.Context.Current.ArgsBuilder.AppendLine($"{indent}逻辑操作: {condition.Operator}");
                if (condition.Children != null)
                {
                    for (int i = 0; i < condition.Children.Count; i++)
                    {
                        this.Context.Current.ArgsBuilder.AppendLine($"{indent}  条件 {i + 1}:");
                        RecordConditionDetails(condition.Children[i], engine, level + 2);
                    }
                }
            }
            else if (condition.Type == ConditionInfoType.Leaf)
            {
                var leftValue = condition.ColumnValue?.GetDataValue(condition.ColumnValue.DataType, this.Context, engine);
                var rightValue = condition.Value?.GetDataValue(condition.ColumnValue?.DataType, this.Context, engine);

                this.Context.Current.ArgsBuilder.AppendLine($"{indent}左值: {JsonHelper.Serialize(leftValue)} ({leftValue?.GetType().Name ?? "null"})");
                this.Context.Current.ArgsBuilder.AppendLine($"{indent}操作符: {condition.Operator}");
                this.Context.Current.ArgsBuilder.AppendLine($"{indent}右值: {JsonHelper.Serialize(rightValue)} ({rightValue?.GetType().Name ?? "null"})");

                // 计算这个条件的结果
                var conditionResult = FlowUtils.GetConditionValue(condition, engine: engine, context: this.Context);
                this.Context.Current.ArgsBuilder.AppendLine($"{indent}条件结果: {conditionResult}");
            }
        }

        [Function("controlBreak", "中断迭代")]
        public void ControlBreak()
        {
            this.Context.BreakCancellationTokenSource?.Cancel();
            this.Context.BreakCancellationTokenSource?.Token.ThrowIfCancellationRequested();
        }

        [Function("controlContinue", "跳过当前迭代")]
        public void ControlContinue()
        {
            this.Context.ContinueCancellationTokenSource?.Cancel();
            this.Context.ContinueCancellationTokenSource?.Token.ThrowIfCancellationRequested();
        }

        [Function("runScript", "运行脚本")]
        public object RunScript(DataScript data)
        {
            var engine = this.GetEngine();
            var jsValue = engine.Evaluate(data.Script);
            return jsValue.GetObject();
        }

        [Function("dataTransaction", "数据库事务")]
        public void TransactionData()
        {

        }

        [Function("controlTryCatch", "异常捕获")]
        public void TryCatch()
        {

        }

        [Function("controlDelay", "延迟")]
        public async Task Delay(DataDelay data)
        {
            var engine = FlowUtils.GetEngine(Context);

            var millisecondsDelay = data.MillisecondsDelay.GetDataValue<int?>(engine, globalData: Context.globalData);
            if (millisecondsDelay == null)
            {
                throw new CustomException("延迟时间不能为空");
            }
            if (this.Context.Persistence)
            {
                this.Context.Current.ArgsBuilder = new StringBuilder();
                this.Context.Current.ArgsBuilder.AppendLine(millisecondsDelay.Value.ToString());
            }
            await Task.Delay(millisecondsDelay.Value);
        }

        [Function("throwError", "抛出异常")]
        public async Task ThrowError(DataThrowError data)
        {
            var errorMessageObj = GetDataValue(data.ErrorMessage);
            var errorMessage = Convert.ToString(errorMessageObj);
            await this.Context.SqlLog.Warn($"{data.Name}：{errorMessage}", true);
            throw new CustomSkipException(errorMessage);
        }
    }
}
